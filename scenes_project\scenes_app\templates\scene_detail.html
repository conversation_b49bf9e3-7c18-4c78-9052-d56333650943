{% extends 'base.html' %}
{% block content %}
<div class="max-w-5xl mx-auto mobile-container">
  <!-- Navigation Header -->
  <div class="mb-4 sm:mb-6 lg:mb-8 flex flex-col gap-3 sm:gap-4 mobile-mb-4 mobile-section-spacing">
    <nav class="flex items-center space-x-2 sm:space-x-4 mobile-gap-2">
      <a href="/" class="group inline-flex items-center px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl bg-gray-50 text-gray-600 font-medium hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200 text-sm touch-target mobile-btn mobile-px-3 mobile-py-2 mobile-text-sm">
        <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="hidden sm:inline mobile-hide">All Scenes</span>
        <span class="sm:hidden mobile-show">Back</span>
      </a>
      <div class="text-gray-300 hidden sm:block mobile-hide">/</div>
      <span class="text-sm text-gray-500 font-medium mobile-text-xs">Scene #{{ scene.id }}</span>
    </nav>

    <div class="mobile-btn-group flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 mobile-gap-3">
      <!-- Favorite Button -->
      <button id="favorite-btn" 
              data-scene-id="{{ scene.id }}"
              class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl touch-target mobile-px-4 mobile-py-3
                     {% if is_favorited %}
                       bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500
                     {% else %}
                       bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 focus:ring-gray-400
                     {% endif %}">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-200 flex-shrink-0" 
             fill="{% if is_favorited %}currentColor{% else %}none{% endif %}" 
             stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
        <span id="favorite-text" class="text-sm sm:text-base mobile-text-sm truncate">
          {% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}
        </span>
      </button>

      <!-- Edit Button -->
      <a href="{% url 'edit_scene' scene.id %}" 
         class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base touch-target mobile-px-4 mobile-py-3 mobile-text-sm">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-200 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        <span class="truncate">Edit Scene</span>
      </a>

      <!-- Random Button -->
      <a href="{% url 'random_scene' %}" 
         class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base touch-target mobile-px-4 mobile-py-3 mobile-text-sm">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Random Scene
        <span class="ml-2 text-xs opacity-75 hidden sm:inline">(Press R)</span>
      </a>
    </div>
  </div>

  <!-- Main Content Card -->
  <article class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
    <!-- Hero Header -->
    <header class="relative px-4 sm:px-8 py-6 sm:py-8 bg-gradient-to-br from-gray-50 to-white border-b border-gray-100">
      <div class="absolute top-4 right-4 text-xs text-gray-400 font-mono bg-white px-2 py-1 rounded-md border">
        ID: {{ scene.id }}
      </div>
      
      <div class="max-w-4xl">
        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 mb-4 sm:mb-6 leading-tight">
          {{ scene.title }}
        </h1>
        
        <!-- Enhanced Metadata -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-4 sm:mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="min-w-0">
              <div class="text-sm text-gray-500">Location</div>
              <div class="font-semibold text-gray-900 truncate">{{ scene.country }}</div>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 sm:w-5 sm:h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="min-w-0">
              <div class="text-sm text-gray-500">Setting</div>
              <div class="font-semibold text-gray-900 truncate">{{ scene.setting }}</div>
            </div>
          </div>
          
          <div class="flex items-center space-x-3 sm:col-span-2 lg:col-span-1">
            <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="min-w-0">
              <div class="text-sm text-gray-500">Emotion</div>
              <div class="font-semibold text-gray-900 truncate">{{ scene.emotion }}</div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Character & Atmosphere Details -->
    <section class="px-4 sm:px-8 py-6 sm:py-8 border-b border-gray-100">
      <div class="mb-6 sm:mb-8">
        <h2 class="text-xl sm:text-2xl font-light text-gray-900 mb-2">Character Profiles</h2>
        <p class="text-gray-600 text-sm sm:text-base">Detailed descriptions of the characters and their environment</p>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-6 sm:mb-8">
        <!-- Effeminate Character -->
        <div class="bg-gradient-to-br from-pink-50 via-rose-50 to-pink-50 rounded-2xl p-6 sm:p-8 border border-pink-100">
          <div class="flex items-center mb-4 sm:mb-6">
            <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-pink-400 to-rose-400 flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
              <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="min-w-0">
              <h3 class="text-lg sm:text-xl font-semibold text-pink-900">Effeminate Character</h3>
              <div class="text-sm text-pink-700 font-medium">Age {{ scene.effeminate_age }}</div>
            </div>
          </div>
          
          <div class="space-y-3 sm:space-y-4">
            <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-pink-200/50">
              <div class="text-sm font-medium text-pink-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                Appearance
              </div>
              <p class="text-pink-700 text-sm leading-relaxed">{{ scene.details.effeminate.appearance|default:'Classic beauty with delicate features' }}</p>
            </div>
            
            <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-pink-200/50">
              <div class="text-sm font-medium text-pink-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                </svg>
                Hair Style
              </div>
              <p class="text-pink-700 text-sm leading-relaxed">{{ scene.details.effeminate.hair|default:'Elegantly styled with attention to detail' }}</p>
            </div>
            
            <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-pink-200/50">
              <div class="text-sm font-medium text-pink-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" clip-rule="evenodd"></path>
                </svg>
                Attire
              </div>
              <p class="text-pink-700 text-sm leading-relaxed">{{ scene.details.effeminate.clothing|default:'Beautifully crafted traditional garments' }}</p>
            </div>
          </div>
        </div>

        <!-- Masculine Character -->
        <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-blue-100">
          <div class="flex items-center mb-4 sm:mb-6">
            <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-blue-400 to-indigo-400 flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
              <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="min-w-0">
              <h3 class="text-lg sm:text-xl font-semibold text-blue-900">Masculine Character</h3>
              <div class="text-sm text-blue-700 font-medium">Age {{ scene.masculine_age }}</div>
            </div>
          </div>
          
          <div class="space-y-3 sm:space-y-4">
            <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-blue-200/50">
              <div class="text-sm font-medium text-blue-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                Appearance
              </div>
              <p class="text-blue-700 text-sm leading-relaxed">{{ scene.details.masculine.appearance|default:'Strong presence with commanding features' }}</p>
            </div>
            
            <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-blue-200/50">
              <div class="text-sm font-medium text-blue-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                </svg>
                Hair Style
              </div>
              <p class="text-blue-700 text-sm leading-relaxed">{{ scene.details.masculine.hair|default:'Well-groomed with distinguished styling' }}</p>
            </div>
            
            <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-blue-200/50">
              <div class="text-sm font-medium text-blue-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" clip-rule="evenodd"></path>
                </svg>
                Attire
              </div>
              <p class="text-blue-700 text-sm leading-relaxed">{{ scene.details.masculine.clothing|default:'Refined traditional clothing with elegant details' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Atmosphere Section -->
      <div class="bg-gradient-to-br from-amber-50 via-orange-50 to-amber-50 rounded-2xl p-6 sm:p-8 border border-amber-100">
        <div class="flex items-center mb-4 sm:mb-6">
          <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-amber-400 to-orange-400 flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="min-w-0">
            <h3 class="text-lg sm:text-xl font-semibold text-amber-900">Scene Atmosphere</h3>
            <div class="text-sm text-amber-700">Environmental details and ambiance</div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-amber-200/50">
            <div class="text-sm font-medium text-amber-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.477.859h4z"></path>
              </svg>
              Lighting
            </div>
            <p class="text-amber-700 text-sm leading-relaxed">{{ scene.details.atmosphere.lighting|default:'Warm, ambient lighting creating intimacy' }}</p>
          </div>
          
          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-amber-200/50">
            <div class="text-sm font-medium text-amber-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
              Fragrance
            </div>
            <p class="text-amber-700 text-sm leading-relaxed">{{ scene.details.atmosphere.scent|default:'Subtle, romantic fragrances in the air' }}</p>
          </div>
          
          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-amber-200/50 sm:col-span-2 lg:col-span-1">
            <div class="text-sm font-medium text-amber-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-2.21-.896-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 12a5.984 5.984 0 01-.757 2.828 1 1 0 01-1.415-1.414A3.984 3.984 0 0013 12a3.983 3.983 0 00-.172-1.414 1 1 0 010-1.415z" clip-rule="evenodd"></path>
              </svg>
              Soundscape
            </div>
            <p class="text-amber-700 text-sm leading-relaxed">{{ scene.details.atmosphere.sound|default:'Gentle, melodious sounds enhancing the mood' }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Scene Description -->
    <section class="px-4 sm:px-8 py-6 sm:py-8">
      <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
        <div class="min-w-0">
          <h2 class="text-xl sm:text-2xl font-light text-gray-900 mb-2">Complete Scene Description</h2>
          <p class="text-gray-600 text-sm sm:text-base">The full narrative prompt for this romantic scene</p>
        </div>
        
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div class="text-sm text-gray-500">
            <span class="font-medium">{{ scene.full_text|length }}</span> characters
          </div>
          <button id="copy-btn" 
                  class="group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl bg-gradient-to-r from-primary to-primary-dark text-white font-medium hover:from-primary-dark hover:to-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base" 
                  aria-label="Copy prompt" data-scene-id="{{ scene.id }}">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            Copy to Clipboard
          </button>
        </div>
      </div>
      
      <div class="relative">
        <div id="prompt-text" class="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 sm:p-8 text-gray-800 leading-relaxed whitespace-pre-line border border-gray-200 shadow-inner font-serif text-sm sm:text-base">{{ scene.full_text }}</div>
        
        <!-- Reading Progress Indicator -->
        <div class="mt-4 flex flex-col sm:flex-row sm:items-center justify-between text-xs text-gray-500 gap-2">
          <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
            <span>📖 Reading time: ~{{ scene.full_text|length|floatformat:0|add:"0"|slice:":1" }} min</span>
            <span>📝 Words: ~{{ scene.full_text|wordcount }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span>Scroll to read</span>
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
      </div>
    </section>
  </article>
</div>
{% endblock %}
