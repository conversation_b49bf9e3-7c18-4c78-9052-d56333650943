/* Mobile-First Responsive CSS - Enhanced and Consolidated */

/* Essential Mobile Variables */
:root {
  --touch-target: 44px;
  --touch-target-large: 48px;
  --mobile-padding: 1rem;
  --mobile-gap: 0.75rem;
  --mobile-border-radius: 0.75rem;
}

/* Core Mobile Fixes */
@media (max-width: 768px) {
  /* Prevent accidental touches during scroll - Apply to main content areas */
  body {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  main,
  .main-content,
  .scene-list,
  .edit-form,
  .scroll-container {
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }

  /* Ensure all interactive elements are touch-friendly with proper touch handling */
  button,
  a,
  input,
  select,
  textarea,
  .touch-target {
    min-height: var(--touch-target);
    min-width: var(--touch-target);
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    user-select: none;
    -webkit-user-select: none;
  }

  /* Prevent iOS zoom on form inputs */
  input,
  textarea,
  select {
    font-size: 16px !important;
    user-select: text;
    -webkit-user-select: text;
  }

  /* Enhanced mobile button improvements */
  button,
  .btn,
  a[role="button"] {
    padding: 0.75rem 1rem;
    border-radius: var(--mobile-border-radius);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
  }

  /* Improved button active states for better feedback */
  button:active,
  .btn:active,
  a[role="button"]:active {
    transform: scale(0.96);
    transition: transform 0.1s ease;
  }

  /* Enhanced Scene card mobile optimization */
  .scene-card {
    margin-bottom: 1rem;
    touch-action: pan-y;
  }

  .scene-card-icon-button {
    width: var(--touch-target) !important;
    height: var(--touch-target) !important;
    padding: 0.75rem !important;
    touch-action: manipulation;
    border-radius: var(--mobile-border-radius) !important;
  }

  .scene-card-main-button {
    min-height: var(--touch-target);
    padding: 0.75rem 1rem;
    touch-action: manipulation;
    border-radius: var(--mobile-border-radius) !important;
  }

  /* Enhanced Mobile navigation */
  .mobile-nav-item {
    padding: 1rem;
    min-height: var(--touch-target);
    display: flex;
    align-items: center;
    touch-action: manipulation;
    border-radius: var(--mobile-border-radius);
  }

  /* Enhanced Hero buttons */
  .hero-button {
    min-height: var(--touch-target);
    padding: 0.75rem 1rem;
    touch-action: manipulation;
    border-radius: var(--mobile-border-radius) !important;
  }

  /* Enhanced Pagination buttons */
  .pagination-btn {
    min-height: var(--touch-target);
    min-width: var(--touch-target);
    padding: 0.75rem;
    touch-action: manipulation;
    border-radius: var(--mobile-border-radius) !important;
  }

  /* Grid layout - single column on mobile */
  .grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Mobile spacing */
  .mobile-container {
    padding-left: var(--mobile-padding);
    padding-right: var(--mobile-padding);
  }

  /* Remove hover effects on mobile */
  .scene-card:hover {
    transform: none;
  }
}

/* Enhanced Touch feedback with better performance */
@media (max-width: 768px) {
  .touch-feedback {
    transition: transform 0.1s ease;
    will-change: transform;
  }

  .touch-feedback:active {
    transform: scale(0.96);
  }

  /* Apply consistent touch feedback to all interactive elements */
  button:active,
  a:active,
  .scene-card-main-button:active,
  .scene-card-icon-button:active,
  .hero-button:active,
  .pagination-btn:active,
  .mobile-nav-item:active {
    transform: scale(0.96);
    transition: transform 0.1s ease;
  }

  /* Prevent double-tap zoom on buttons */
  button,
  .scene-card-main-button,
  .scene-card-icon-button,
  .hero-button,
  .pagination-btn {
    touch-action: manipulation;
    -ms-touch-action: manipulation;
  }
}

/* Mobile viewport fix for iOS */
@media (max-width: 768px) {
  .mobile-vh-fix {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
  }
}

/* Simplified mobile pagination */
@media (max-width: 640px) {
  .pagination-container .hidden.lg\\:flex {
    display: none !important;
  }
  
  #page-size-selector,
  #jump-to-page,
  .keyboard-shortcuts {
    display: none !important;
  }
}

/* Simple Mobile Menu Styles - Like web app */
@media (max-width: 768px) {
  /* Mobile menu items hover effects */
  #mobile-menu a:hover {
    background: #f9fafb !important;
  }

  #mobile-menu a:active {
    background: #f3f4f6 !important;
  }
}/* Mobil
e-Optimized Edit Scene Page */
@media (max-width: 768px) {
  /* Edit page specific styles */
  .edit-scene-container {
    padding: 1rem;
  }

  /* Mobile form sections */
  .edit-section {
    margin-bottom: 1.5rem;
    border-radius: 1rem;
  }

  .edit-section-header {
    padding: 1rem;
  }

  .edit-section-content {
    padding: 1rem;
  }

  /* Mobile form inputs */
  .edit-input,
  .edit-textarea {
    font-size: 16px !important;
    min-height: 44px;
    padding: 0.75rem;
    border-radius: 0.75rem;
    border: 1px solid #d1d5db;
    width: 100%;
    transition: all 0.2s ease;
  }

  .edit-textarea {
    min-height: 80px;
    resize: vertical;
  }

  .edit-input:focus,
  .edit-textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  /* Mobile action buttons */
  .edit-action-btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    width: 100%;
    margin-bottom: 0.75rem;
  }

  .edit-action-btn:active {
    transform: scale(0.98);
  }

  /* Mobile grid adjustments */
  .edit-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Mobile navigation */
  .edit-nav {
    margin-bottom: 1rem;
  }

  .edit-nav-btn {
    min-height: 44px;
    padding: 0.75rem;
    border-radius: 0.75rem;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Character count */
  .char-count {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
  }

  /* Mobile labels */
  .edit-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
  }
}

/* Old mobile menu button styles removed - now handled in Enhanced Mobile Menu Styles section */

/* Enhanced Touch Handling for Better Scroll Experience */
@media (max-width: 768px) {
  /* Prevent accidental touches during scroll - Apply to scrollable areas */
  .scroll-safe,
  main,
  .main-content,
  .scene-list,
  .edit-form,
  .grid {
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }

  /* Allow manipulation only on interactive elements */
  button,
  a,
  input,
  textarea,
  select,
  .touch-target,
  .scene-card-main-button,
  .scene-card-icon-button,
  .hero-button,
  .pagination-btn,
  .mobile-nav-item {
    touch-action: manipulation;
    -ms-touch-action: manipulation;
  }

  /* Improve scroll performance and prevent momentum scrolling issues */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  /* Prevent text selection on buttons during touch */
  button,
  .scene-card-main-button,
  .scene-card-icon-button,
  .hero-button,
  .pagination-btn {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }

  /* Add scroll margin to prevent content hiding behind fixed headers */
  .scene-card,
  .hero-section {
    scroll-margin-top: 80px;
  }

  /* Improve touch target spacing to prevent accidental touches */
  .scene-card-actions {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .scene-card-button-group {
    gap: 0.75rem;
  }

  /* Add safe area for devices with notches */
  .mobile-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Mobile Safe Areas */
@media (max-width: 768px) {
  /* Handle notched devices */
  .mobile-safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .mobile-safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .mobile-safe-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .mobile-safe-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Duplicate mobile menu styles removed - consolidated above */
/* All mobile menu button and navigation styles are now consolidated in the Enhanced Mobile Menu Styles section above */